import { type TodoProps } from '@shared/types/todo';
import Divider from '@shared/uikit/Divider';
import DividerVertical from '@shared/uikit/Divider/DividerVertical';
import Flex from '@shared/uikit/Flex';
import { RichTextView } from '@shared/uikit/RichText';
import Typography from '@shared/uikit/Typography';
import cnj from '@shared/uikit/utils/cnj';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { FC } from 'react';
import type { ICandidateTodo } from '@shared/types/candidates';
import DateAndTimeShower from '../DateAndTimeShower';
import UserInfo from '../UserInfo';
import classes from './index.module.scss';

export interface TodoItemBodyProps {
  item: ICandidateTodo;
  classNames?: {
    title?: string;
    description?: string;
  };
  displayCreator?: true;
  variant?: 'card' | 'preview';
}

const TodoItemBody: FC<TodoItemBodyProps> = (props) => {
  const { item, classNames, displayCreator, variant = 'card' } = props;
  const { t } = useTranslation();
  const showAssignee = variant === 'card' || item.assigneeUser?.name;
  const showCreator =
    displayCreator && (variant === 'card' || item.creator?.name);

  return null;
  return (
    <Flex className={classes.body}>
      {variant === 'card' ? (
        <Flex>
          <Typography
            className={cnj(
              classes.title,
              item.status?.value === 'DONE' ? classes.done : '',
              classNames?.title
            )}
            color="smoke_coal"
            font="700"
          >
            {item.title}
          </Typography>

          <RichTextView
            html={item?.description || ''}
            className={classNames?.description}
            typographyProps={{
              color: 'secondaryDisabledText',
              size: 14,
              height: 18,
            }}
            showMore
          />
        </Flex>
      ) : null}
      {showCreator || showAssignee ? <Divider /> : null}
      {showAssignee ? (
        <UserInfo
          title={`${item.assigneeUser.name || ''} ${item.assigneeUser.surname || ''}`}
          description={item.assigneeUser.occupationName as string}
          image={item.assigneeUser.croppedImageUrl}
          layoutTitle={t('assignee')}
        />
      ) : null}
      {showCreator ? (
        <UserInfo
          title={`${item.creator.name || ''} ${item.creator.surname || ''}`}
          description={item.creator.occupationName as string}
          image={item.creator.croppedImageUrl}
          layoutTitle={t('creator')}
        />
      ) : null}
      <Divider />
      <Flex flexDir="row" className={classes.dates}>
        <DateAndTimeShower title={t('start_date_time')} date={item.start} />
        <DividerVertical className={classes.divider} />
        <DateAndTimeShower title={t('end_date_time')} date={item.end} />
      </Flex>
    </Flex>
  );
};

export default TodoItemBody;
