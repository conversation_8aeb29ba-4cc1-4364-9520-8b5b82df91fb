import React, { useMemo } from 'react';
import Button from 'shared/uikit/Button';
import Flex from 'shared/uikit/Flex';
import Form from 'shared/uikit/Form';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import SubmitButton from 'shared/uikit/Form/SubmitButton';
import ModalFooter from 'shared/uikit/Modal/ModalFooter';
import cnj from 'shared/uikit/utils/cnj';
import useMedia from 'shared/uikit/utils/useMedia';
import useTranslation from 'shared/utils/hooks/useTranslation';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import FixedRightSideModalDialog from 'shared/uikit/Modal/FixedRightSideModalDialog';
import {
  addSavedFilter,
  editSavedFilter,
  removeSavedFilter,
} from '@shared/utils/api/candidates';
import useOpenConfirm from '@shared/uikit/Confirmation/useOpenConfirm';
import Typography from '@shared/uikit/Typography';
import useToast from '@shared/uikit/Toast/useToast';
import { useSearchState } from '@shared/contexts/search/search.provider';
import removeEmptyFromObject from '@shared/utils/toolkit/removeEmptyFromObject';
import useUpdateQueryData from '@shared/utils/hooks/useUpdateQueryData';
import { QueryKeys } from '@shared/utils/constants';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import classes from '../SearchFiltersModal/SearchFiltersModal.component.module.scss';
import { searchFilterQueryParams } from '@shared/constants/search';
import useCustomParams from '@shared/utils/hooks/useCustomParams';

export interface SaveSearchFiltersModalProps {
  onClose: () => void;
  groups: Array<any>;
}

const SaveSearchFiltersModal: React.FC<SaveSearchFiltersModalProps> = ({
  onClose,
  groups,
}) => {
  const { businessPage } = useGetAppObject();
  const { isTabletAndLess } = useMedia();
  const { t } = useTranslation();
  const toast = useToast();
  const { openConfirmDialog, closeConfirm } = useOpenConfirm();
  const selectedItem = useSearchState('saveSearchFilterModalData')?.item;
  const isEdit = Boolean(selectedItem?.id);
  const { handleChangeParams } = useCustomParams();

  const savedFiltersQueryCache = useUpdateQueryData([
    QueryKeys.getAllSavedFilters,
    businessPage.id,
  ]);

  const onCloseHandler = () => {
    onClose();
  };

  const onClickOutside = () => {
    if (isTabletAndLess) return;
    onCloseHandler();
  };

  const onSuccess = (isDelete?: boolean) => {
    savedFiltersQueryCache.refetch();
    if (isDelete) {
      handleChangeParams({
        remove: ['newSaved'],
        add: {
          refresh: 'true',
          selectFirst: 'true',
        },
      });
    } else {
      handleChangeParams({
        add: {
          refresh: 'true',
          [searchFilterQueryParams.candidateSearchType]: 'saved',
          newSaved: 'true',
        },
        remove: ['selectFirst'],
      });
    }
    onCloseHandler();
    closeConfirm();
    toast({
      type: 'success',
      icon: isDelete
        ? 'filter_deleted'
        : isEdit
          ? 'filter_updated'
          : 'filter_saved',
      message: isDelete
        ? 'yt_filters_d_sucss'
        : isEdit
          ? 'yt_filters_u_sucss'
          : 'yt_filters_s_sucss',
    });
  };
  const deleteWithConfirm = () => {
    openConfirmDialog({
      title: t('delete'),
      message: t('delete_item_confirmation'),
      confirmButtonText: t('delete'),
      cancelButtonText: t('cancel'),
      isAjaxCall: true,
      apiProps: {
        func: () => removeSavedFilter(selectedItem.id),
        onSuccess: () => onSuccess(true),
      },
    });
  };
  const transformHandler = (values) => {
    const result = Object.keys(values)?.reduce((acc, key) => {
      const value = values?.[key];
      const getFormValue = formGroups.find((i) => i.name === key)?.getFormValue;
      if (!hasValue(value)) return acc;
      if (getFormValue) {
        acc[key] = getFormValue(value);
      } else if (Array.isArray(value)) {
        acc[key] = value.map((item) => item.value || item);
      } else {
        acc[key] = value;
      }
      return acc;
    }, {});
    return result;
  };
  const openConfirmHandler = (val) => {
    openConfirmDialog({
      title: isEdit ? t('update_filters') : t('save_filters'),
      message: (
        <Form
          apiFunc={isEdit ? editSavedFilter : addSavedFilter}
          transform={({ name }) => ({ ...val, name })}
          initialValues={{ name: selectedItem?.name || '' }}
          onSuccess={onSuccess}
        >
          <Typography className="mb-16_20">{t('give_y_f_a_m_n_tr')}</Typography>
          <DynamicFormBuilder
            groups={[
              {
                cp: 'input',
                name: 'name',
                label: t('filter_name'),
                required: true,
              },
            ]}
          />
          <Flex className="!flex-row gap-8 !justify-end pt-20">
            <Button label={t('cancel')} schema="ghost" onClick={closeConfirm} />
            <SubmitButton label={isEdit ? t('update') : t('save')} />
          </Flex>
        </Form>
      ),
    });
  };
  const formGroups = useMemo(() => {
    const baseGroups = groups.map((item) => ({
      ...item,
      divider: undefined,
      wrapStyle: cnj(classes.formItem, item.wrapStyle),
      labelProps: { ...(item?.labelProps || {}), color: 'smoke_coal' },
      optionsVariant:
        item?.cp === 'checkBoxGroup'
          ? isTabletAndLess
            ? 'modalcheckboxgroup'
            : 'dropdown'
          : undefined,
    }));
    return baseGroups;
  }, [groups, isTabletAndLess, t]);

  const initialValues = isEdit
    ? removeEmptyFromObject(selectedItem)
    : { source: 'ALL' };

  return (
    <FixedRightSideModalDialog
      onBack={onCloseHandler}
      onClose={onCloseHandler}
      onClickOutside={onClickOutside}
      wide
      visibleBackdrop
      fullBackdrop
      contentClassName="!max-w-full"
      doubleColumn
    >
      <ModalHeaderSimple
        backButtonProps={{ onClick: onCloseHandler }}
        closeButtonProps={{ onClick: onCloseHandler }}
        className={classes.header}
        visibleHeaderDivider={isTabletAndLess}
        title={t('save_filters')}
      />
      <Form
        className={cnj(classes.form)}
        initialValues={initialValues}
        onSuccess={openConfirmHandler}
        local
        enableReinitialize
        transform={transformHandler}
      >
        {() => (
          <Flex style={{ height: '100%' }}>
            <Flex className={cnj(classes.wrapper)}>
              <Flex className={classes.modalBody}>
                <DynamicFormBuilder
                  groups={formGroups?.filter((i) => i.cp !== 'list')}
                  className={cnj(
                    classes.formGroupItemFullWidth,
                    classes.formItemFullWidthFirst
                  )}
                />
              </Flex>
            </Flex>
            <ModalFooter className={classes.submitWrap}>
              <Button
                onClick={isEdit ? deleteWithConfirm : onCloseHandler}
                className={classes.btn}
                label={isEdit ? t('delete') : t('cancel')}
                schema="ghost"
                labelFont="700"
              />
              <Flex className={classes.divider} />
              <SubmitButton
                className={classes.btn}
                label={isEdit ? t('update') : t('save')}
              />
            </ModalFooter>
          </Flex>
        )}
      </Form>
    </FixedRightSideModalDialog>
  );
};

export default SaveSearchFiltersModal;

function hasValue(value: string | any[]) {
  return Array.isArray(value) ? Object.keys(value)?.length > 0 : !!value;
}
