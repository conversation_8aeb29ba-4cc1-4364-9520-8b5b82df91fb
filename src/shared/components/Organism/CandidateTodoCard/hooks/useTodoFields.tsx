import useGetAppObject from '@shared/hooks/useGetAppObject';
import cnj from '@shared/uikit/utils/cnj';
import { searchPerson } from '@shared/utils/api/search';
import { schedulesDb } from '@shared/utils/constants';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { useMemo, useRef } from 'react';
import { useFormikContext } from 'formik';
import type { ICandidateTodo } from '@shared/types/candidates';
import IconButton from '@shared/uikit/Button/IconButton';
import PopperMenu from '@shared/uikit/PopperMenu';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import FormCard from '@shared/components/molecules/FormCard';
import fileApi from 'shared/utils/api/file';
import { storageEndPoints } from '@shared/utils/constants/servicesEndpoints';
import storageApis from '@shared/utils/api/storage';
import type { AttachmentPickerProps } from '@shared/uikit/AttachmentPicker/AttachmentPicker.component';
import { PrivateAttachmentsList } from '@shared/components/molecules/Attachments';
import classes from './TodoFields.module.scss';

export function useTodoFields() {
  const datesPopperRef = useRef();
  const assigneePopperRef = useRef();
  const { t } = useTranslation();
  const { authUser } = useGetAppObject();
  const { values, setFieldValue, dirty } = useFormikContext<ICandidateTodo>();

  const TITLE = useMemo(
    () => ({
      name: 'title',
      cp: 'input',
      showEmoji: false,
      label: t('write_todo'),
      labelProps: { color: 'colorIconForth2' },
      required: true,
    }),
    [t]
  );
  const DESCRIPTION = useMemo(
    () => ({
      name: 'description',
      cp: 'richtext',
      showEmoji: false,
      label: t('description'),
      changeWithDebounce: false,
      fixMentionDropdown: false,
      isFocus: true,
      disableNewLineWithEnter: true,
      noLeft: true,
      labelProps: { color: 'colorIconForth2' },
      isSidebar: false,
      required: true,
    }),
    [t]
  );
  const ASSIGNEE = useMemo(
    () => ({
      name: 'assigneeUser',
      label: t('assignee'),
      cp: 'avatarAsyncAutoComplete',
      rightIconClassName: '!bg-transparent',
      apiFunc: searchPerson,
      maxLength: 100,
      forceVisibleError: true,
      normalizer: (data: { content: any[] }) =>
        data?.content?.map((item) => ({
          ...item,
          value: item.id,
          label: item.fullName,
          helperText: item.username,
          image: item.croppedImageUrl,
        })),
      visibleRightIcon: true,
    }),
    [t]
  );
  const START_DATE = useMemo(
    () => ({
      name: 'startDate',
      cp: 'datePicker',
      closeOnSelect: true,
      calendarClassName: classes.datepicker,
      rightIconClassName: '!bg-transparent',
      variant: 'input',
      label: t('start_date'),
    }),
    [t]
  );
  const START_TIME = useMemo(
    () => ({
      name: 'startTime',
      cp: 'dropdownSelect',
      rightIconClassName: '!bg-transparent',
      options: schedulesDb.timeOptions,
      label: t('start_time'),
      doNotUseTranslation: true,
      rightIconProps: { name: 'clock' },
      displayName: values.startTime?.label,
    }),
    [values, t]
  );
  const END_DATE = useMemo(
    () => ({
      name: 'endDate',
      cp: 'datePicker',
      closeOnSelect: true,
      calendarClassName: classes.datepicker,
      rightIconClassName: '!bg-transparent',
      variant: 'input',
      label: t('end_date'),
    }),
    [t]
  );
  const END_TIME = useMemo(
    () => ({
      name: 'endTime',
      cp: 'dropdownSelect',
      rightIconClassName: '!bg-transparent',
      options: schedulesDb.timeOptionsEnd,
      label: t('end_time'),
      doNotUseTranslation: true,
      rightIconProps: { name: 'clock' },
      displayName: values.endTime?.label,
    }),
    [t, values.endTime?.label]
  );

  const ATTACHMENT = useMemo(
    () => ({
      name: 'attachment',
      cp: 'attachmentPicker',
      showUploadList: false,
      type: 'image',
      uploadUrl: storageEndPoints.uploadPrivateFile,
      getApi: storageApis.getFile,
      visibleOptionalLabel: false,
      forceVisibleError: true,
      uploadApi: fileApi.uploadFile,
      classNames: {
        dropzoneOvveride: '',
      },
      onChange: (newFiles: AttachmentPickerProps['value']) => {
        const { fileIds = [] } = values ?? {};
        const newFileIds = newFiles.map(({ id }) => id);
        setTimeout(() => {
          setFieldValue('fileIds', [...newFileIds, ...fileIds]);
        }, 1000);
      },
    }),
    [t]
  );

  const ATTACHMENT_LIST = useMemo(
    () => ({
      name: 'fileIds',
      cp: ({ value }) => <PrivateAttachmentsList ids={value} horizontal />,
    }),
    []
  );
  const DATES_POPPER = useMemo(
    () => ({
      id: 'DATES_POPPER',
      position: 'control-bar',
      cp: () => (
        <PopperMenu
          placement="top"
          menuClassName="!w-[400px] !p-0 !border-0"
          disableCloseOnClickInSide
          ref={datesPopperRef}
          clickOutsideExceptions={{
            elementIds: [
              'autocomplete-options', // For dropdown selects (time dropdowns)
              'LOBOX_DATE_PICKER_BOTTOM_SHEET', // For mobile date picker
              'auto-complete-wrapper-id', // AutoComplete wrapper
            ],
            cssSelectors: [
              '.react-datepicker', // React DatePicker calendar
              '.react-datepicker-popper', // React DatePicker portal container
              '.react-datepicker__tab-loop', // React DatePicker tab loop
              '[class*="react-datepicker"]', // Any element with react-datepicker in class name
              '[class*="autocomplete"]', // Any autocomplete related elements
              '.POPPER_CLASS', // Other popper menus
              '[class*="AutoComplete_options"]', // AutoComplete options CSS module classes
              '[class*="AutoComplete_root"]', // AutoComplete root CSS module classes
              '[class*="AutoComplete_show"]', // AutoComplete show CSS module classes
              '[class*="options"]', // Generic options classes
              '[class*="dropdown"]', // Generic dropdown classes
              '[data-portal="true"]', // Elements marked as portals
              '[role="listbox"]', // ARIA listbox elements
              '[role="option"]', // ARIA option elements
              '.uikit3', // AutoComplete layer class
            ],
          }}
          onOpen={() => {
            assigneePopperRef.current?.close();
          }}
          buttonComponent={
            <IconButton
              size="sm"
              name="calendar-days"
              colorSchema="transparent"
              type="far"
            />
          }
        >
          <FormCard
            local
            transform={datesPopperTransformer}
            initialValues={values}
            onSuccess={(data) => {
              setFieldValue('start', data.start);
              setFieldValue('end', data.end);
              datesPopperRef.current?.close();
            }}
            cardWrapperProps={{
              classNames: { container: '!p-12 !gap-12 !pb-0' },
            }}
            classNames={{ footer: '!p-12 !border-0' }}
            onSecondaryClick={() => datesPopperRef.current?.close()}
          >
            <DynamicFormBuilder
              groups={[START_DATE, START_TIME, END_DATE, END_TIME]}
              className="gap-12"
            />
          </FormCard>
        </PopperMenu>
      ),
    }),
    [END_DATE, END_TIME, START_DATE, START_TIME, setFieldValue, values]
  );

  const ASSIGNEE_POPPER = useMemo(
    () => ({
      id: 'ASSIGNEE_POPPER',
      position: 'control-bar',
      cp: () => (
        <PopperMenu
          placement="top"
          ref={assigneePopperRef}
          menuClassName="!w-[400px] !p-0 !border-0"
          disableCloseOnClickInSide
          clickOutsideExceptions={{
            elementIds: [
              'autocomplete-options', // For dropdown selects and autocomplete
              'auto-complete-wrapper-id', // AutoComplete wrapper
            ],
            cssSelectors: [
              '[class*="autocomplete"]', // Any autocomplete related elements
              '.POPPER_CLASS', // Other popper menus
              '[class*="AutoComplete_options"]', // AutoComplete options CSS module classes
              '[class*="AutoComplete_root"]', // AutoComplete root CSS module classes
              '[class*="AutoComplete_show"]', // AutoComplete show CSS module classes
              '[class*="options"]', // Generic options classes
              '[class*="dropdown"]', // Generic dropdown classes
              '[data-portal="true"]', // Elements marked as portals
              '[role="listbox"]', // ARIA listbox elements
              '[role="option"]', // ARIA option elements
              '.uikit3', // AutoComplete layer class
            ],
          }}
          onOpen={() => {
            datesPopperRef.current?.close();
          }}
          buttonComponent={
            <IconButton
              size="sm"
              name="user-time"
              colorSchema="transparent"
              type="far"
            />
          }
        >
          <FormCard
            local
            initialValues={values}
            onSuccess={(data) => {
              setFieldValue('assigneeUser', data.assigneeUser);
              assigneePopperRef.current?.close();
            }}
            cardWrapperProps={{
              classNames: { container: '!p-12 !gap-12 !pb-0' },
            }}
            classNames={{ footer: '!p-12 !border-0' }}
            onSecondaryClick={() => datesPopperRef.current?.close()}
          >
            <DynamicFormBuilder groups={[ASSIGNEE]} className="gap-12" />
          </FormCard>
        </PopperMenu>
      ),
    }),
    [ASSIGNEE, values]
  );

  return useMemo(
    () => ({
      TITLE,
      DESCRIPTION,
      ASSIGNEE,
      START_DATE,
      END_DATE,
      START_TIME,
      END_TIME,
      ATTACHMENT,
      ATTACHMENT_LIST,
      DATES_POPPER,
      ASSIGNEE_POPPER,
      TITLE_COMPOSE: {
        ...TITLE,
        label: '',
        bordered: false,
        placeholder: t('write_todo'),
        style: '!h-[21px]',
        inputWrapClassName: '!bg-transparent !border-0 !h-auto',
        wrapBtnClassName: '!h-[21px]',
        inputStyle: cnj('!p-1', classes.placeholder),
        divider: dirty,
      },
      DESCRIPTION_COMPOSE: {
        ...DESCRIPTION,
        variant: 'comment-input',
        labelProps: {
          className: classes.labelstyle,
        },
        className: classes.qlEditor,
        divider: true,
      },
      ATTACHMENT_ICON: {
        ...ATTACHMENT,
        position: 'control-bar',
        buttonComponent: (
          <IconButton
            disabled
            colorSchema="transparent"
            size="sm"
            name="paperclip"
          />
        ),
      },
    }),
    [
      t,
      dirty,
      TITLE,
      DESCRIPTION,
      DATES_POPPER,
      ASSIGNEE_POPPER,
      ASSIGNEE,
      START_DATE,
      END_DATE,
      START_TIME,
      END_TIME,
      ATTACHMENT,
      ATTACHMENT_LIST,
    ]
  );
}

function datesPopperTransformer(
  data: ICandidateTodo
): Pick<ICandidateTodo, 'start' | 'end'> {
  const startDate = data.startDate?.split('T')[0];
  const endDate = data.endDate?.split('T')[0];

  const startTime = `${data.startTime?.value ?? '00:00'}:00`;
  const endTime = `${data.endTime?.value ?? '23:59'}:00`;
  return {
    start: startDate ? `${startDate}T${startTime}` : undefined,
    end: endDate ? `${endDate}T${endTime}` : undefined,
  };
}
