import React, {
  cloneElement,
  isValidElement,
  useEffect,
  useImperativeHandle,
  useRef,
} from 'react';
import preventClickHandler from 'shared/utils/toolkit/preventClickHandler';
import useStateCallback from 'shared/utils/hooks/useStateCallback';
import Popper from '@mui/material/Popper';
import isFunction from 'lodash/isFunction';
import { flushSync } from 'react-dom';
import type { PopperMenuProps } from 'shared/types/components/PopperMenu.type';
import cnj from '../utils/cnj';
import BottomSheet from '../BottomSheet';
import Media from '../Media';
import Flex from '../Flex';
import classes from './index.module.scss';

const POPPER_CLASS = 'POPPER_CLASS';

export type PopperMenuRefType = {
  open?: () => void;
  close?: () => void;
};

const PopperMenu = ({
  buttonComponent,
  menuClassName,
  popperContainerClassName,
  bottomSheetClassName,
  children,
  offsetX = 0,
  offsetY = 4,
  placement,
  disablePortal,
  hasArrow,
  clickCallBack,
  showWithHover,
  hoverDelay = 300,
  closeDelay = 1,
  closeOnScroll,
  disableCloseOnClickInSide,
  noDrawer = false,
  onClose,
  onCloseOutside,
  onOpen,
  disableCloseOnClickOutSide,
  classNames,
  noBottomSheetForMobile = false,
  popperWidth,
  useInside = false,
  disabled,
  ref,
  clickOutsideExceptions,
}: PopperMenuProps) => {
  const [visible, setVisible] = useStateCallback(false);
  const ButtonComponent = isValidElement(buttonComponent)
    ? buttonComponent
    : buttonComponent(visible!);
  const currentElementRef = useRef<HTMLElement>(null);
  const inTimeout = useRef(false);
  const referenceRef = useRef<{
    contains: Function;
  }>(null);
  const popperRef = useRef<HTMLElement>(null);
  const [arrowRef, setArrowRef] = React.useState(null);
  const bottomSheetRef = useRef(null);

  const handleSetInvisible = React.useCallback(
    () => setVisible(false, onClose),
    [onClose]
  );

  const handleSetInvisibleOutside = React.useCallback(
    () => setVisible(false, () => onCloseOutside?.()),
    [onCloseOutside]
  );

  useImperativeHandle(ref, () => ({
    close() {
      handleSetInvisible();
    },
    open() {
      setVisible(true, onOpen);
    },
  }));

  const visiblePopper = (e: any, value?: boolean) => {
    try {
      clickCallBack?.(e);
      setVisible((v) => {
        if (value) v = !value;
        if (!v && isFunction(onOpen)) {
          onOpen();
        } else if (v && isFunction(onClose)) {
          onClose();
        }
        return !v;
      });
    } catch (error) {
      console.log(error);
    }
  };

  const toggleVisibleMouseLeave = (e: any) => {
    try {
      if (showWithHover) {
        visiblePopper(e, false);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const onMouseLeaveButtonOrPopper = async (e: any) => {
    if (!showWithHover) return;
    if (showWithHover && !visible) return;
    if (inTimeout.current) return;
    inTimeout.current = true;

    await new Promise((res) => {
      setTimeout(() => {
        res(true);
      }, hoverDelay / 2);
    });
    inTimeout.current = false;
    if (
      popperRef?.current?.contains(currentElementRef?.current) ||
      referenceRef?.current?.contains(currentElementRef?.current)
    )
      return;
    toggleVisibleMouseLeave(e);
  };

  const toggleVisible = async (e: any) => {
    if (disablePortal && !useInside) return false;
    preventClickHandler(e);

    if (isFunction(ButtonComponent?.props?.onClick)) {
      ButtonComponent?.props?.onClick(e);
    }

    if (isFunction(ButtonComponent?.props?.onMouseEnter)) {
      ButtonComponent?.props?.onMouseEnter(e);
    }
    try {
      if (showWithHover) {
        if (visible) return false;
        await new Promise((res) => {
          setTimeout(() => {
            res(true);
          }, hoverDelay);
        });
        if (inTimeout.current) return false;

        if (
          popperRef?.current?.contains(currentElementRef.current) ||
          referenceRef?.current?.contains(currentElementRef.current)
        ) {
          visiblePopper(e, true);
          return true;
        }
        return false;
      }
      visiblePopper(e);
      return true;
    } catch {
      return false;
    }
  };

  const Button = cloneElement(ButtonComponent, {
    ref: referenceRef,
    ...(showWithHover
      ? {
          onMouseEnter: toggleVisible,
          onMouseLeave: onMouseLeaveButtonOrPopper,
          onClick: () => (visible ? setVisible(false) : undefined),
        }
      : {
          onClick: toggleVisible,
        }),
  });

  const onClickHandler = (e: any) => {
    if (!disableCloseOnClickInSide) {
      closeHandler(e);
    }
  };

  const close = (e: Event) => {
    if (e?.target && popperRef?.current?.contains?.(e.target)) return;
    flushSync(handleSetInvisible);
  };

  const closeRef = React.useRef(close);
  closeRef.current = close;

  React.useEffect(() => {
    const closeListener = (evt: Event) => {
      closeRef.current(evt);
    };
    if (closeOnScroll) {
      window.addEventListener('scroll', closeListener, true);
    }

    return () => {
      window.removeEventListener('scroll', closeListener, true);
    };
  }, [closeOnScroll]);

  const documentClick = (event: any) => {
    if (!disableCloseOnClickOutSide) {
      const popperList = Array.from(
        document.getElementsByClassName(POPPER_CLASS)
      );

      const isClickOnExceptionElement =
        clickOutsideExceptions?.elementIds?.some((id) => {
          const element = document.getElementById(id);

          return element?.contains(event.target);
        });

      const isClickOnExceptionSelector =
        clickOutsideExceptions?.cssSelectors?.some((selector) => {
          try {
            return event.target.closest && event.target.closest(selector);
          } catch (e) {
            // Invalid selector, ignore
            return false;
          }
        });

      if (
        !referenceRef.current?.contains(event.target) &&
        !popperRef.current?.contains(event.target) &&
        !popperList.some((el) => el?.contains(event.target)) &&
        !isClickOnExceptionElement &&
        !isClickOnExceptionSelector
      ) {
        if (onCloseOutside) {
          handleSetInvisibleOutside();
        } else {
          handleSetInvisible();
        }
      }
    }
  };

  const documentClickRef = React.useRef(documentClick);
  documentClickRef.current = documentClick;

  const closeHandler = (e: any) => {
    setTimeout(() => {
      handleSetInvisible();
    }, closeDelay);
    e?.stopPropagation();
  };

  const closeHandlerRef = React.useRef(closeHandler);
  closeHandlerRef.current = closeHandler;

  useEffect(() => {
    const setCurrentElementRef = (e: any) => {
      if (e?.target) currentElementRef.current = e.target;
    };
    const documentClickListener = (evt: MouseEvent) => {
      documentClickRef.current(evt);
    };
    const closeHandlerListener = (evt: Event) => {
      closeHandlerRef.current(evt);
    };

    document.addEventListener('mousedown', documentClickListener);
    window.addEventListener('resize', closeHandlerListener);
    window.addEventListener('mousemove', setCurrentElementRef);

    return () => {
      document.removeEventListener('mousedown', documentClickListener);
      window.removeEventListener('resize', closeHandlerListener);
      window.addEventListener('mousemove', setCurrentElementRef);
    };
  }, []);

  if (disabled) {
    return ButtonComponent;
  }
  return (
    <>
      {Button}
      <Media greaterThan={noDrawer ? undefined : 'tablet'}>
        <Popper
          ref={popperRef}
          disablePortal={disablePortal}
          open={visible as boolean}
          className={cnj(
            POPPER_CLASS,
            classes.popperContainer,
            popperContainerClassName
          )}
          anchorEl={referenceRef.current}
          style={
            popperWidth
              ? { width: popperWidth(referenceRef.current?.clientWidth) }
              : undefined
          }
          placement={placement}
          modifiers={[
            {
              name: 'flip',
              enabled: true,
            },
            {
              name: 'preventOverflow',
              enabled: true,
              options: {
                rootBoundary: 'viewpeort',
              },
            },
            {
              name: 'arrow',
              enabled: hasArrow,
              options: {
                element: arrowRef,
              },
            },
            {
              name: 'offset',
              options: {
                offsetX,
                offsetY,
              },
            },
          ]}
        >
          <Flex
            className={cnj(classes.popperWrapper, menuClassName)}
            onClick={onClickHandler}
            onMouseLeave={onMouseLeaveButtonOrPopper}
          >
            {children}
          </Flex>
          {hasArrow ? (
            <Flex
              as="span"
              className={cnj(classes.arrow, classNames?.arrow)}
              ref={setArrowRef}
            />
          ) : null}
        </Popper>
      </Media>
      {!noDrawer && (
        <Media lessThan="midDesktop">
          {noBottomSheetForMobile ? (
            <Flex>{visible && children}</Flex>
          ) : (
            <BottomSheet
              ref={bottomSheetRef}
              open={visible}
              onRequestClose={closeHandler}
              modalElementClass={bottomSheetClassName}
            >
              {children}
            </BottomSheet>
          )}
        </Media>
      )}
    </>
  );
};

export default PopperMenu;
